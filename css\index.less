// 取消微信浏览器点击的蓝色背景
// *{
//     -webkit-touch-callout:none;
//     -webkit-user-select:none; 
//     -moz-user-select:none;
//     -ms-user-select:none;
//     user-select:none;
//     -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
//     -webkit-user-select: none;
//     -moz-user-focus: none;
//     -moz-user-select: none
// }
body,div,h1,h2,h3,h4,h5,h6,html,li,p,span{font-size:3.5vw}
.musicbtn {
    width: 6vw;
    height: 6vw;
    right: 3vw;
    z-index: 8;
}

.warp {
    width: 100vw;
    // height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;

    .page {
        width: 100%;
        height: 100vh;
        min-height: 177vw;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        color: #000;
        background:#fcdb5a url(../img/bj_2.jpg)  no-repeat center top / 100% auto;    
        .bg2{
            width: 100vw;
            position: absolute;
            bottom: 0;
            pointer-events: none;
        } 
        .title{
            width: 80vw;
            position: absolute;
            top: 24vw;
        }
        .area{
            width: 86.6667vw;
            height: 39.6vw;
            position: absolute;
            bottom: 23vw;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 4vw;
            border-radius: 4vw;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            box-shadow: 0 2vw 8vw rgba(0, 0, 0, 0.15);
            border: 0.5vw solid rgba(255, 255, 255, 0.3);
            
            input{
                margin-top: 2vw;
                width: 69.3333vw;
                height: 9.3333vw;
                background-color: #fff;
                text-align: center;
                border: 0.3vw solid #e0e0e0;
                border-radius: 2vw;
                font-size: 3.5vw;
                color: #333;
                outline: none;
                transition: all 0.3s ease;
                box-shadow: inset 0 0.5vw 1vw rgba(0, 0, 0, 0.05);

                &::placeholder {
                    color: #999;
                    font-size: 3.2vw;
                }

                &:focus {
                    border-color: #fcdb5a;
                    box-shadow: 0 0 0 0.5vw rgba(252, 219, 90, 0.2);
                    transform: translateY(-0.2vw);
                }

                &:hover {
                    border-color: #fcdb5a;
                }
            }

            .button1{
                margin-top: 4vw;
                width: 32vw;
                cursor: pointer;
                transition: all 0.3s ease;
                filter: drop-shadow(0 1vw 2vw rgba(0, 0, 0, 0.2));

                &:hover {
                    transform: translateY(-0.5vw) scale(1.02);
                    filter: drop-shadow(0 1.5vw 3vw rgba(0, 0, 0, 0.25));
                }

                &:active {
                    transform: translateY(0.2vw) scale(0.98);
                    filter: drop-shadow(0 0.5vw 1vw rgba(0, 0, 0, 0.15));
                }
            }
            .button4{
                margin-top: 20vw;
                width: 32vw;
                height: 9.2vw;
                background: url(../img/button2.png) no-repeat center top / 100% 100%;
                position: relative;
                cursor: pointer;
                transition: all 0.3s ease;
                filter: drop-shadow(0 1vw 2vw rgba(0, 0, 0, 0.2));

                &:hover {
                    transform: translateY(-0.5vw) scale(1.02);
                    filter: drop-shadow(0 1.5vw 3vw rgba(0, 0, 0, 0.25));
                }

                &:active {
                    transform: translateY(0.2vw) scale(0.98);
                    filter: drop-shadow(0 0.5vw 1vw rgba(0, 0, 0, 0.15));
                }
            }


        }
    }


}

.fc {
    justify-content: center;
}
.logo{
    width: 30.8vw;
    position: absolute;
    top: 4vw;
    left: 4vw;
}
.mask {
    z-index: 8;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
    color: #d90011;
    .popup {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        .close {
            width: 9vw;
            height: 9vw;
            background: url(../img/close.png) no-repeat center top / 100% 100%;
            position: absolute;
            bottom: 0vw;
        }
    }
    .popup1 {
        width: 76.16vw;
        height: 67.52vw;
        background: url(../img/popup1.png) no-repeat center top / 100% 100%;
    }
}